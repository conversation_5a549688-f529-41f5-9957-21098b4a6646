const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');
const eslintPluginPrettierRecommended = require('eslint-plugin-prettier/recommended');

module.exports = (async function config() {
  const { default: love } = await import('eslint-config-love');

  return defineConfig([
    expoConfig,
    eslintPluginPrettierRecommended,
    {
      ...love,
      files: ['**/*.js', '**/*.ts', '**/*.tsx', '**/*.jsx'],
    },
    {
      ignores: ['dist/*', 'node_modules/*', '.expo/*', 'ios/*', 'android/*'],
    },
  ]);
})();
