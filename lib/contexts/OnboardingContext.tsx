import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
  useMemo,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  OnboardingData,
  PersonalInfo,
  DiseaseHistory,
  Medications,
  personalInfoSchema,
  diseaseHistorySchema,
  medicationSchema,
  onboardingSchema,
} from '@/types/onboarding';
import { useRouter } from 'expo-router';
import { Alert } from 'react-native';

const STORAGE_KEY = '@onboarding_data';

interface OnboardingContextType {
  currentStep: number;
  isSubmitting: boolean;
  errors: Record<string, string>;
  personalInfo: Partial<PersonalInfo>;
  diseaseHistory: Partial<DiseaseHistory>;
  medications: Partial<Medications>;
  finalConfirmation: boolean;
  setPersonalInfo: (data: Partial<PersonalInfo>) => void;
  setDiseaseHistory: (data: Partial<DiseaseHistory>) => void;
  setMedications: (data: Partial<Medications>) => void;
  setFinalConfirmation: (value: boolean) => void;
  nextStep: () => void;
  previousStep: () => void;
  handleSubmit: () => Promise<void>;
  addTestResult: () => void;
  removeTestResult: (index: number) => void;
  addMedication: () => void;
  removeMedication: (index: number) => void;
  setCurrentStep: (step: number) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

export function OnboardingProvider({
  children,
}: Readonly<{ children: ReactNode }>) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [personalInfo, setPersonalInfo] = useState<Partial<PersonalInfo>>({
    firstName: '',
    middleName: '',
    lastName: '',
    dateOfBirth: undefined,
    email: '',
    phone: '',
    emergencyContact: {
      fullName: '',
      relationship: '',
      primaryPhone: '',
      secondaryPhone: '',
    },
  });

  const [diseaseHistory, setDiseaseHistory] = useState<Partial<DiseaseHistory>>(
    {
      primaryDiagnosis: '',
      otherDiagnosis: '',
      diagnosisDate: undefined,
      diseaseStage: undefined,
      secondaryConditions: [],
      testResults: [],
    }
  );

  const [medications, setMedications] = useState<Partial<Medications>>({
    medications: [],
  });

  const [finalConfirmation, setFinalConfirmation] = useState(false);

  useEffect(() => {
    loadSavedData();
  }, []);

  useEffect(() => {
    saveDataToStorage();
  }, [personalInfo, diseaseHistory, medications, finalConfirmation]);

  const loadSavedData = useCallback(async () => {
    try {
      const savedData = await AsyncStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed = JSON.parse(savedData);
        if (parsed.personalInfo) setPersonalInfo(parsed.personalInfo);
        if (parsed.diseaseHistory) setDiseaseHistory(parsed.diseaseHistory);
        if (parsed.medications) setMedications(parsed.medications);
        if (parsed.finalConfirmation)
          setFinalConfirmation(parsed.finalConfirmation);
      }
    } catch (error) {
      console.warn('Failed to load saved data:', error);
    }
  }, []);

  const saveDataToStorage = useCallback(async () => {
    try {
      const dataToSave = {
        personalInfo,
        diseaseHistory,
        medications,
        finalConfirmation,
        lastSaved: new Date().toISOString(),
      };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.warn('Failed to save data:', error);
    }
  }, [personalInfo, diseaseHistory, medications, finalConfirmation]);

  const validateCurrentStep = useCallback(() => {
    setErrors({});
    try {
      switch (currentStep) {
        case 1:
          personalInfoSchema.parse(personalInfo);
          break;
        case 2:
          diseaseHistorySchema.parse(diseaseHistory);
          break;
        case 3:
          medicationSchema.parse(medications);
          break;
        case 4:
          if (!finalConfirmation) {
            throw new Error('You must confirm to proceed');
          }
          break;
      }
      return true;
    } catch (error: any) {
      if (error.errors) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err: any) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      } else {
        setErrors({ general: error.message });
      }
      return false;
    }
  }, [
    currentStep,
    personalInfo,
    diseaseHistory,
    medications,
    finalConfirmation,
  ]);

  const nextStep = useCallback(() => {
    if (validateCurrentStep()) {
      if (currentStep < 4) {
        setCurrentStep(currentStep + 1);
      } else {
        handleSubmit();
      }
    }
  }, [currentStep, validateCurrentStep]);

  const previousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    try {
      const completeData: OnboardingData = {
        personalInfo: personalInfo as PersonalInfo,
        diseaseHistory: diseaseHistory as DiseaseHistory,
        medications: medications as Medications,
        finalConfirmation,
      };
      onboardingSchema.parse(completeData);
      await new Promise((resolve) => setTimeout(resolve, 2000));
      await AsyncStorage.removeItem(STORAGE_KEY);
      Alert.alert(
        'Success!',
        'Your information has been submitted successfully. Welcome to your liver health journey!',
        [
          {
            text: 'Continue',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    } catch (error: any) {
      Alert.alert(
        'Submission Error',
        'There was an error submitting your information. Please check your data and try again.',
        [{ text: 'OK' }]
      );
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [personalInfo, diseaseHistory, medications, finalConfirmation, router]);

  const addTestResult = useCallback(() => {
    const newTestResult = {
      testType: '',
      dateConducted: new Date(),
      result: '',
      unit: '',
      labName: '',
    };
    setDiseaseHistory({
      ...diseaseHistory,
      testResults: [...(diseaseHistory.testResults || []), newTestResult],
    });
  }, [diseaseHistory]);

  const removeTestResult = useCallback(
    (index: number) => {
      const updatedResults =
        diseaseHistory.testResults?.filter((_, i) => i !== index) || [];
      setDiseaseHistory({
        ...diseaseHistory,
        testResults: updatedResults,
      });
    },
    [diseaseHistory]
  );

  const addMedication = useCallback(() => {
    const newMedication = {
      name: '',
      dosage: '',
      unit: 'mg',
      frequency: '',
      timingRequirements: [],
      startDate: new Date(),
      specialInstructions: '',
      prescribingDoctor: '',
    };
    setMedications({
      ...medications,
      medications: [...(medications.medications || []), newMedication],
    });
  }, [medications]);

  const removeMedication = useCallback(
    (index: number) => {
      const updatedMedications =
        medications.medications?.filter((_, i) => i !== index) || [];
      setMedications({
        ...medications,
        medications: updatedMedications,
      });
    },
    [medications]
  );

  const value = useMemo(() => {
    return {
      currentStep,
      isSubmitting,
      errors,
      personalInfo,
      diseaseHistory,
      medications,
      finalConfirmation,
      setPersonalInfo,
      setDiseaseHistory,
      setMedications,
      setFinalConfirmation,
      nextStep,
      previousStep,
      handleSubmit,
      addTestResult,
      removeTestResult,
      addMedication,
      removeMedication,
      setCurrentStep,
    };
  }, [
    currentStep,
    isSubmitting,
    errors,
    personalInfo,
    diseaseHistory,
    medications,
    finalConfirmation,
    nextStep,
    previousStep,
    handleSubmit,
    addTestResult,
    removeTestResult,
    addMedication,
    removeMedication,
  ]);

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
