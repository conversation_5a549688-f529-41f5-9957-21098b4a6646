import { StateCreator } from 'zustand';
import { Medication, MedicationReminder } from '../types';

export interface MedicationState {
  medications: Medication[];
  reminders: MedicationReminder[];
  isLoading: boolean;
  error: string | null;
}

export interface MedicationActions {
  addMedication: (medication: Omit<Medication, 'id'>) => void;
  updateMedication: (id: string, updates: Partial<Medication>) => void;
  removeMedication: (id: string) => void;
  addReminder: (reminder: Omit<MedicationReminder, 'id'>) => void;
  markReminderTaken: (reminderId: string, taken: boolean) => void;
  getTodaysReminders: () => MedicationReminder[];
  getUpcomingReminders: () => MedicationReminder[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type MedicationSlice = MedicationState & MedicationActions;

export const createMedicationSlice: StateCreator<
  MedicationSlice,
  [],
  [],
  MedicationSlice
> = (set, get) => ({
  // Initial state
  medications: [
    {
      id: '1',
      name: 'Lactulose',
      dosage: '15',
      unit: 'ml',
      frequency: 'Twice daily',
      timingRequirements: ['With food'],
      startDate: new Date('2024-01-01'),
      prescribingDoctor: 'Dr. Sarah <PERSON>',
    },
    {
      id: '2',
      name: 'Rifaximin',
      dosage: '550',
      unit: 'mg',
      frequency: 'Twice daily',
      timingRequirements: ['With food'],
      startDate: new Date('2024-01-01'),
      prescribingDoctor: 'Dr. Sarah Johnson',
    },
    {
      id: '3',
      name: 'Vitamin D',
      dosage: '1000',
      unit: 'IU',
      frequency: 'Once daily',
      timingRequirements: ['With food'],
      startDate: new Date('2024-01-01'),
      prescribingDoctor: 'Dr. Sarah Johnson',
    },
  ],
  reminders: [
    {
      id: '1',
      medicationId: '1',
      time: '8:00 AM',
      taken: false,
      date: new Date(),
    },
    {
      id: '2',
      medicationId: '2',
      time: '12:00 PM',
      taken: true,
      date: new Date(),
    },
    {
      id: '3',
      medicationId: '3',
      time: '6:00 PM',
      taken: false,
      date: new Date(),
    },
  ],
  isLoading: false,
  error: null,

  // Actions
  addMedication: (medication: Omit<Medication, 'id'>) => {
    const newMedication: Medication = {
      ...medication,
      id: Date.now().toString(),
    };

    set((state) => ({
      medications: [...state.medications, newMedication],
    }));
  },

  updateMedication: (id: string, updates: Partial<Medication>) => {
    set((state) => ({
      medications: state.medications.map((med) =>
        med.id === id ? { ...med, ...updates } : med,
      ),
    }));
  },

  removeMedication: (id: string) => {
    set((state) => ({
      medications: state.medications.filter((med) => med.id !== id),
      reminders: state.reminders.filter(
        (reminder) => reminder.medicationId !== id,
      ),
    }));
  },

  addReminder: (reminder: Omit<MedicationReminder, 'id'>) => {
    const newReminder: MedicationReminder = {
      ...reminder,
      id: Date.now().toString(),
    };

    set((state) => ({
      reminders: [...state.reminders, newReminder],
    }));
  },

  markReminderTaken: (reminderId: string, taken: boolean) => {
    set((state) => ({
      reminders: state.reminders.map((reminder) =>
        reminder.id === reminderId ? { ...reminder, taken } : reminder,
      ),
    }));
  },

  getTodaysReminders: () => {
    const { reminders } = get();
    const today = new Date().toDateString();

    return reminders.filter(
      (reminder) => reminder.date.toDateString() === today,
    );
  },

  getUpcomingReminders: () => {
    const { reminders } = get();
    const now = new Date();

    return reminders
      .filter((reminder) => reminder.date >= now && !reminder.taken)
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
});
