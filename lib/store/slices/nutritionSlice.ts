import { StateCreator } from "zustand";
import { FoodItem, FoodLog, HealthMetric } from "../types";
import { searchFoodDatabase as searchFoodDB } from "../../data/foodDatabase";

export interface NutritionState {
  foodLogs: FoodLog[];
  dailyMetrics: HealthMetric[];
  isLoading: boolean;
  error: string | null;
}

export interface NutritionActions {
  addFoodLog: (
    foodItem: FoodItem,
    mealType: "breakfast" | "lunch" | "dinner" | "snack",
  ) => void;
  removeFoodLog: (logId: string) => void;
  updateFoodLog: (logId: string, updates: Partial<FoodLog>) => void;
  addHealthMetric: (metric: Omit<HealthMetric, "id">) => void;
  getDailyNutrition: (date?: Date) => {
    sodium: number;
    protein: number;
    fat: number;
    calories: number;
  };
  getMealLogs: (mealType: string, date?: Date) => FoodLog[];
  searchFoodDatabase: (query: string) => FoodItem[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type NutritionSlice = NutritionState & NutritionActions;

export const createNutritionSlice: StateCreator<
  NutritionSlice,
  [],
  [],
  NutritionSlice
> = (set, get) => ({
  // Initial state
  foodLogs: [],
  dailyMetrics: [],
  isLoading: false,
  error: null,

  // Actions
  addFoodLog: (
    foodItem: FoodItem,
    mealType: "breakfast" | "lunch" | "dinner" | "snack",
  ) => {
    const newLog: FoodLog = {
      id: Date.now().toString(),
      foodItem,
      mealType,
      timestamp: new Date(),
    };

    set((state) => ({
      foodLogs: [...state.foodLogs, newLog],
    }));

    // Add health metrics
    const { addHealthMetric } = get();
    addHealthMetric({
      type: "sodium",
      value: foodItem.sodium,
      unit: "mg",
      date: new Date(),
    });
    addHealthMetric({
      type: "protein",
      value: foodItem.protein,
      unit: "g",
      date: new Date(),
    });
    addHealthMetric({
      type: "fat",
      value: foodItem.fat,
      unit: "g",
      date: new Date(),
    });
    addHealthMetric({
      type: "calories",
      value: foodItem.calories,
      unit: "cal",
      date: new Date(),
    });
  },

  removeFoodLog: (logId: string) => {
    set((state) => ({
      foodLogs: state.foodLogs.filter((log) => log.id !== logId),
    }));
  },

  updateFoodLog: (logId: string, updates: Partial<FoodLog>) => {
    set((state) => ({
      foodLogs: state.foodLogs.map((log) =>
        log.id === logId ? { ...log, ...updates } : log
      ),
    }));
  },

  addHealthMetric: (metric: Omit<HealthMetric, "id">) => {
    const newMetric: HealthMetric = {
      ...metric,
      id: Date.now().toString(),
    };

    set((state) => ({
      dailyMetrics: [...state.dailyMetrics, newMetric],
    }));
  },

  getDailyNutrition: (date = new Date()) => {
    const { foodLogs } = get();
    const today = date.toDateString();

    const todayLogs = foodLogs.filter(
      (log) => log.timestamp.toDateString() === today,
    );

    return todayLogs.reduce(
      (total, log) => ({
        sodium: total.sodium + log.foodItem.sodium,
        protein: total.protein + log.foodItem.protein,
        fat: total.fat + log.foodItem.fat,
        calories: total.calories + log.foodItem.calories,
      }),
      { sodium: 0, protein: 0, fat: 0, calories: 0 },
    );
  },

  getMealLogs: (mealType: string, date = new Date()) => {
    const { foodLogs } = get();
    const today = date.toDateString();

    return foodLogs.filter(
      (log) =>
        log.mealType === mealType && log.timestamp.toDateString() === today,
    );
  },

  searchFoodDatabase: (query: string) => {
    return searchFoodDB(query);
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
});
