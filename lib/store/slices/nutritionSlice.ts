import { StateCreator } from 'zustand';
import { FoodItem, FoodLog, HealthMetric } from '../types';

export interface NutritionState {
  foodLogs: FoodLog[];
  dailyMetrics: HealthMetric[];
  foodDatabase: FoodItem[];
  isLoading: boolean;
  error: string | null;
}

export interface NutritionActions {
  addFoodLog: (
    foodItem: FoodItem,
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
  ) => void;
  removeFoodLog: (logId: string) => void;
  updateFoodLog: (logId: string, updates: Partial<FoodLog>) => void;
  addHealthMetric: (metric: Omit<HealthMetric, 'id'>) => void;
  getDailyNutrition: (date?: Date) => {
    sodium: number;
    protein: number;
    fat: number;
    calories: number;
  };
  getMealLogs: (mealType: string, date?: Date) => FoodLog[];
  searchFoodDatabase: (query: string) => FoodItem[];
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type NutritionSlice = NutritionState & NutritionActions;

// Sample food database
const sampleFoodDatabase: FoodItem[] = [
  {
    id: '1',
    name: 'Grilled Chicken Breast',
    sodium: 74,
    protein: 31,
    fat: 3,
    calories: 165,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '2',
    name: 'Canned Soup',
    sodium: 890,
    protein: 5,
    fat: 2,
    calories: 80,
    quantity: '1 cup',
    isLiverFriendly: false,
    warnings: ['High sodium content - may cause fluid retention'],
  },
  {
    id: '3',
    name: 'Fresh Salmon',
    sodium: 52,
    protein: 22,
    fat: 12,
    calories: 208,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '4',
    name: 'Processed Cheese',
    sodium: 1671,
    protein: 25,
    fat: 33,
    calories: 375,
    quantity: '100g',
    isLiverFriendly: false,
    warnings: [
      'Very high sodium',
      'High fat content - may worsen liver condition',
    ],
  },
];

export const createNutritionSlice: StateCreator<
  NutritionSlice,
  [],
  [],
  NutritionSlice
> = (set, get) => ({
  // Initial state
  foodLogs: [],
  dailyMetrics: [],
  foodDatabase: sampleFoodDatabase,
  isLoading: false,
  error: null,

  // Actions
  addFoodLog: (
    foodItem: FoodItem,
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
  ) => {
    const newLog: FoodLog = {
      id: Date.now().toString(),
      foodItem,
      mealType,
      timestamp: new Date(),
    };

    set((state) => ({
      foodLogs: [...state.foodLogs, newLog],
    }));

    // Add health metrics
    const { addHealthMetric } = get();
    addHealthMetric({
      type: 'sodium',
      value: foodItem.sodium,
      unit: 'mg',
      date: new Date(),
    });
    addHealthMetric({
      type: 'protein',
      value: foodItem.protein,
      unit: 'g',
      date: new Date(),
    });
    addHealthMetric({
      type: 'fat',
      value: foodItem.fat,
      unit: 'g',
      date: new Date(),
    });
    addHealthMetric({
      type: 'calories',
      value: foodItem.calories,
      unit: 'cal',
      date: new Date(),
    });
  },

  removeFoodLog: (logId: string) => {
    set((state) => ({
      foodLogs: state.foodLogs.filter((log) => log.id !== logId),
    }));
  },

  updateFoodLog: (logId: string, updates: Partial<FoodLog>) => {
    set((state) => ({
      foodLogs: state.foodLogs.map((log) =>
        log.id === logId ? { ...log, ...updates } : log,
      ),
    }));
  },

  addHealthMetric: (metric: Omit<HealthMetric, 'id'>) => {
    const newMetric: HealthMetric = {
      ...metric,
      id: Date.now().toString(),
    };

    set((state) => ({
      dailyMetrics: [...state.dailyMetrics, newMetric],
    }));
  },

  getDailyNutrition: (date = new Date()) => {
    const { foodLogs } = get();
    const today = date.toDateString();

    const todayLogs = foodLogs.filter(
      (log) => log.timestamp.toDateString() === today,
    );

    return todayLogs.reduce(
      (total, log) => ({
        sodium: total.sodium + log.foodItem.sodium,
        protein: total.protein + log.foodItem.protein,
        fat: total.fat + log.foodItem.fat,
        calories: total.calories + log.foodItem.calories,
      }),
      { sodium: 0, protein: 0, fat: 0, calories: 0 },
    );
  },

  getMealLogs: (mealType: string, date = new Date()) => {
    const { foodLogs } = get();
    const today = date.toDateString();

    return foodLogs.filter(
      (log) =>
        log.mealType === mealType && log.timestamp.toDateString() === today,
    );
  },

  searchFoodDatabase: (query: string) => {
    const { foodDatabase } = get();
    return foodDatabase.filter((food) =>
      food.name.toLowerCase().includes(query.toLowerCase()),
    );
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
});
