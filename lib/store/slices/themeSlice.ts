import { StateCreator } from 'zustand';
import { Appearance, ColorSchemeName } from 'react-native';

export type ThemeMode = 'light' | 'dark' | 'system';

export interface Colors {
  // Background colors
  background: string;
  surface: string;
  surfaceSecondary: string;
  surfaceElevated: string;

  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;
  textInverse: string;

  // Primary colors
  primary: string;
  primaryLight: string;
  primaryDark: string;

  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Border colors
  border: string;
  borderLight: string;
  borderFocus: string;

  // Shadow colors
  shadow: string;
  shadowLight: string;

  // Tab bar colors
  tabBarBackground: string;
  tabBarBorder: string;
  tabBarActive: string;
  tabBarInactive: string;
}

const lightColors: Colors = {
  background: '#F8FAFC',
  surface: '#FFFFFF',
  surfaceSecondary: '#F1F5F9',
  surfaceElevated: '#FFFFFF',

  text: '#1F2937',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',

  primary: '#14B8A6',
  primaryLight: '#5EEAD4',
  primaryDark: '#0F766E',

  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',

  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  borderFocus: '#14B8A6',

  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',

  tabBarBackground: '#FFFFFF',
  tabBarBorder: '#E5E7EB',
  tabBarActive: '#14B8A6',
  tabBarInactive: '#6B7280',
};

const darkColors: Colors = {
  background: '#0F172A',
  surface: '#1E293B',
  surfaceSecondary: '#334155',
  surfaceElevated: '#475569',

  text: '#F8FAFC',
  textSecondary: '#CBD5E1',
  textTertiary: '#94A3B8',
  textInverse: '#1F2937',

  primary: '#14B8A6',
  primaryLight: '#5EEAD4',
  primaryDark: '#0F766E',

  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',

  border: '#475569',
  borderLight: '#334155',
  borderFocus: '#14B8A6',

  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.3)',

  tabBarBackground: '#1E293B',
  tabBarBorder: '#475569',
  tabBarActive: '#14B8A6',
  tabBarInactive: '#94A3B8',
};

export interface Theme {
  colors: Colors;
  isDark: boolean;
}

export interface ThemeState {
  themeMode: ThemeMode;
  systemColorScheme: ColorSchemeName;
  theme: Theme;
  isDark: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface ThemeActions {
  setThemeMode: (mode: ThemeMode) => void;
  setSystemColorScheme: (colorScheme: ColorSchemeName) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type ThemeSlice = ThemeState & ThemeActions;

const getCurrentIsDark = (
  themeMode: ThemeMode,
  systemColorScheme: ColorSchemeName,
): boolean => {
  if (themeMode === 'system') {
    return systemColorScheme === 'dark';
  }

  return themeMode === 'dark';
};

const getTheme = (isDark: boolean): Theme => ({
  colors: isDark ? darkColors : lightColors,
  isDark,
});

export const createThemeSlice: StateCreator<ThemeSlice, [], [], ThemeSlice> = (
  set,
  get,
) => {
  const initialSystemColorScheme = Appearance.getColorScheme();
  const initialIsDark = getCurrentIsDark('system', initialSystemColorScheme);
  const initialTheme = getTheme(initialIsDark);

  return {
    // Initial state
    themeMode: 'system',
    systemColorScheme: initialSystemColorScheme,
    theme: initialTheme,
    isDark: initialIsDark,
    isLoading: false,
    error: null,

    // Actions
    setThemeMode: (mode: ThemeMode) => {
      set((state) => {
        const newIsDark = getCurrentIsDark(mode, state.systemColorScheme);
        return {
          themeMode: mode,
          isDark: newIsDark,
          theme: getTheme(newIsDark),
        };
      });
    },

    setSystemColorScheme: (colorScheme: ColorSchemeName) => {
      set((state) => {
        const newIsDark = getCurrentIsDark(state.themeMode, colorScheme);
        return {
          systemColorScheme: colorScheme,
          isDark: newIsDark,
          theme: getTheme(newIsDark),
        };
      });
    },

    toggleTheme: () => {
      const { isDark } = get();
      const newMode = isDark ? 'light' : 'dark';
      set((state) => {
        const newIsDark = getCurrentIsDark(newMode, state.systemColorScheme);
        return {
          themeMode: newMode,
          isDark: newIsDark,
          theme: getTheme(newIsDark),
        };
      });
    },

    initializeTheme: () => {
      set({ isLoading: true, error: null });

      try {
        const subscription = Appearance.addChangeListener(({ colorScheme }) => {
          set((state) => {
            const newIsDark = getCurrentIsDark(state.themeMode, colorScheme);
            return {
              systemColorScheme: colorScheme,
              isDark: newIsDark,
              theme: getTheme(newIsDark),
            };
          });
        });

        set({ isLoading: false });

        return () => subscription?.remove();
      } catch (error) {
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to initialize theme',
          isLoading: false,
        });
        return () => {};
      }
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    setError: (error: string | null) => {
      set({ error });
    },
  };
};

// Export types and colors for external use
export { darkColors, lightColors };
