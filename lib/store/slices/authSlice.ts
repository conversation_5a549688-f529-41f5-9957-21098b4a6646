import { StateCreator } from 'zustand';
import { User } from '../types';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  hasCompletedOnboarding: boolean;
}

export interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  setOnboardingComplete: (complete: boolean) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export type AuthSlice = AuthState & AuthActions;

export const createAuthSlice: StateCreator<AuthSlice, [], [], AuthSlice> = (
  set,
  get,
) => ({
  // Initial state
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  hasCompletedOnboarding: false,

  // Actions
  login: async (email: string, password: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock user data - replace with actual API call
      const mockUser: User = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email,
        dateOfBirth: new Date('1980-01-01'),
        phone: '(*************',
        emergencyContact: {
          fullName: 'Jane Doe',
          relationship: 'Spouse',
          primaryPhone: '(*************',
        },
        profile: {
          age: 44,
          weight: 75,
          height: 175,
          liverCondition: 'Mild Fatty Liver',
          diagnosisDate: new Date('2023-06-15'),
          healthcareProvider: 'Dr. Sarah Johnson',
        },
      };

      set({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        hasCompletedOnboarding: true,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Login failed',
        isLoading: false,
      });
    }
  },

  logout: () => {
    set({
      user: null,
      isAuthenticated: false,
      hasCompletedOnboarding: false,
      error: null,
    });
  },

  updateUser: (userData: Partial<User>) => {
    const currentUser = get().user;
    if (currentUser) {
      set({
        user: { ...currentUser, ...userData },
      });
    }
  },

  setOnboardingComplete: (complete: boolean) => {
    set({ hasCompletedOnboarding: complete });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
});
