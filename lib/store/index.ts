import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useShallow } from 'zustand/react/shallow';

// Import slices
import { AuthSlice, createAuthSlice } from './slices/authSlice';
import { createNutritionSlice, NutritionSlice } from './slices/nutritionSlice';
import {
  createMedicationSlice,
  MedicationSlice,
} from './slices/medicationSlice';
import { createSettingsSlice, SettingsSlice } from './slices/settingsSlice';
import { createUISlice, UISlice } from './slices/uiSlice';
import { createThemeSlice, ThemeSlice } from './slices/themeSlice';

// Combined store type
export type AppStore = AuthSlice &
  NutritionSlice &
  MedicationSlice &
  SettingsSlice &
  UISlice &
  ThemeSlice;

// Create the main store with optimized middleware
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((...a) => ({
          ...createAuthSlice(...a),
          ...createNutritionSlice(...a),
          ...createMedicationSlice(...a),
          ...createSettingsSlice(...a),
          ...createUISlice(...a),
          ...createThemeSlice(...a),
        })),
      ),
      {
        name: 'liver-health-store',
        version: 1, // Add versioning for migrations
        partialize: (state) => ({
          // Only persist essential data - exclude large arrays and computed values
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          hasCompletedOnboarding: state.hasCompletedOnboarding,
          settings: state.settings,
          themeMode: state.themeMode,
          // Only persist recent medications, not all historical data
          medications: state.medications,
          // Limit food logs to last 30 days to prevent storage bloat
          foodLogs: state.foodLogs.filter((log) => {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return log.timestamp >= thirtyDaysAgo;
          }),
        }),
        // Add migration logic for future versions
        migrate: (persistedState: any, version: number) => {
          if (version === 0) {
            // Migration from version 0 to 1
            return {
              ...persistedState,
              version: 1,
            };
          }
          return persistedState;
        },
      },
    ),
    {
      name: 'liver-health-store',
    },
  ),
);

// Optimized selectors with shallow comparison for better performance
export const useAuth = () =>
  useAppStore(
    useShallow((state) => ({
      user: state.user,
      isAuthenticated: state.isAuthenticated,
      isLoading: state.isLoading,
      error: state.error,
      hasCompletedOnboarding: state.hasCompletedOnboarding,
      login: state.login,
      logout: state.logout,
      updateUser: state.updateUser,
      setOnboardingComplete: state.setOnboardingComplete,
      clearError: state.clearError,
    })),
  );

export const useNutrition = () =>
  useAppStore(
    useShallow((state) => ({
      foodLogs: state.foodLogs,
      dailyMetrics: state.dailyMetrics,
      foodDatabase: state.foodDatabase,
      isLoading: state.isLoading,
      error: state.error,
      addFoodLog: state.addFoodLog,
      removeFoodLog: state.removeFoodLog,
      updateFoodLog: state.updateFoodLog,
      addHealthMetric: state.addHealthMetric,
      getDailyNutrition: state.getDailyNutrition,
      getMealLogs: state.getMealLogs,
      searchFoodDatabase: state.searchFoodDatabase,
    })),
  );

export const useMedications = () =>
  useAppStore(
    useShallow((state) => ({
      medications: state.medications,
      reminders: state.reminders,
      isLoading: state.isLoading,
      error: state.error,
      addMedication: state.addMedication,
      updateMedication: state.updateMedication,
      removeMedication: state.removeMedication,
      addReminder: state.addReminder,
      markReminderTaken: state.markReminderTaken,
      getTodaysReminders: state.getTodaysReminders,
      getUpcomingReminders: state.getUpcomingReminders,
    })),
  );

export const useSettings = () =>
  useAppStore(
    useShallow((state) => ({
      settings: state.settings,
      isLoading: state.isLoading,
      error: state.error,
      updateThemePreference: state.updateThemePreference,
      updateNotificationSettings: state.updateNotificationSettings,
      updateLanguage: state.updateLanguage,
      updateUnits: state.updateUnits,
      resetSettings: state.resetSettings,
      loadSettings: state.loadSettings,
      saveSettings: state.saveSettings,
    })),
  );

export const useUI = () =>
  useAppStore(
    useShallow((state) => ({
      isPageVisible: state.isPageVisible,
      isLoading: state.isLoading,
      progress: state.progress,
      activeTab: state.activeTab,
      modals: state.modals,
      notifications: state.notifications,
      setPageVisible: state.setPageVisible,
      setLoading: state.setLoading,
      setProgress: state.setProgress,
      setActiveTab: state.setActiveTab,
      openModal: state.openModal,
      closeModal: state.closeModal,
      closeAllModals: state.closeAllModals,
      addNotification: state.addNotification,
      removeNotification: state.removeNotification,
      clearNotifications: state.clearNotifications,
    })),
  );

export const useTheme = () =>
  useAppStore(
    useShallow((state) => ({
      theme: state.theme,
      themeMode: state.themeMode,
      systemColorScheme: state.systemColorScheme,
      isDark: state.isDark,
      isLoading: state.isLoading,
      error: state.error,
      setThemeMode: state.setThemeMode,
      setSystemColorScheme: state.setSystemColorScheme,
      toggleTheme: state.toggleTheme,
      initializeTheme: state.initializeTheme,
      setLoading: state.setLoading,
      setError: state.setError,
    })),
  );

// Memoized computed selectors for better performance
export const useDailyProgress = () =>
  useAppStore(
    useShallow((state) => {
      const nutrition = state.getDailyNutrition();
      return {
        sodium: nutrition.sodium,
        protein: nutrition.protein,
        fat: nutrition.fat,
        calories: nutrition.calories,
        water: 6, // This would come from water tracking
      };
    }),
  );

export const useTodaysMedications = () =>
  useAppStore(
    useShallow((state) => {
      return state.getTodaysReminders().map((reminder) => {
        const medication = state.medications.find(
          (med) => med.id === reminder.medicationId,
        );
        return {
          ...reminder,
          medication,
        };
      });
    }),
  );

// Action creators for complex operations with better error handling
export const useAppActions = () => {
  const store = useAppStore();

  return {
    // Initialize app data with proper error handling
    initializeApp: async () => {
      store.setLoading(true);
      try {
        // Initialize theme first
        const cleanup = store.initializeTheme();

        // Load settings
        await store.loadSettings();

        // Sync theme with settings
        const themeMode = store.settings.themePreference;
        if (themeMode !== store.themeMode) {
          store.setThemeMode(themeMode);
        }

        return cleanup; // Return cleanup function for proper disposal
      } catch (error) {
        console.error('App initialization failed:', error);
        store.addNotification({
          type: 'error',
          message: 'Failed to initialize app',
        });
        throw error; // Re-throw for caller to handle
      } finally {
        store.setLoading(false);
      }
    },

    // Complete onboarding flow with validation
    completeOnboarding: async (userData: any) => {
      if (!userData) {
        throw new Error('User data is required');
      }

      store.setLoading(true);
      try {
        store.updateUser(userData);
        store.setOnboardingComplete(true);
        store.addNotification({
          type: 'success',
          message: 'Welcome! Your profile has been set up successfully.',
        });
      } catch (error) {
        console.error('Onboarding completion failed:', error);
        store.addNotification({
          type: 'error',
          message: 'Failed to complete onboarding',
        });
        throw error;
      } finally {
        store.setLoading(false);
      }
    },

    // Add food with comprehensive validation and notifications
    addFoodWithValidation: (foodItem: any, mealType: any) => {
      if (!foodItem || !mealType) {
        throw new Error('Food item and meal type are required');
      }

      try {
        // Check liver-friendliness and add warnings
        if (!foodItem.isLiverFriendly && foodItem.warnings?.length > 0) {
          store.addNotification({
            type: 'warning',
            message: `${foodItem.name} may not be suitable for liver health. ${foodItem.warnings.join(
              '. ',
            )}`,
          });
        }

        store.addFoodLog(foodItem, mealType);

        // Success notification
        store.addNotification({
          type: 'success',
          message: `${foodItem.name} added to ${mealType}`,
        });

        // Check daily limits and warn if exceeded
        const dailyNutrition = store.getDailyNutrition();
        if (dailyNutrition.sodium > 2300) {
          // Daily sodium limit
          store.addNotification({
            type: 'warning',
            message:
              'Daily sodium limit exceeded. Consider reducing sodium intake.',
          });
        }
      } catch (error) {
        console.error('Failed to add food:', error);
        store.addNotification({
          type: 'error',
          message: 'Failed to add food item',
        });
        throw error;
      }
    },

    // Batch operations for better performance
    batchUpdateSettings: (updates: Partial<any>) => {
      try {
        Object.entries(updates).forEach(([key, value]) => {
          switch (key) {
            case 'theme':
              store.updateThemePreference(value);
              store.setThemeMode(value);
              break;
            case 'notifications':
              store.updateNotificationSettings(value);
              break;
            case 'language':
              store.updateLanguage(value as string);
              break;
            case 'units':
              store.updateUnits(value);
              break;
          }
        });

        store.addNotification({
          type: 'success',
          message: 'Settings updated successfully',
        });
      } catch (error) {
        console.error('Failed to update settings:', error);
        store.addNotification({
          type: 'error',
          message: 'Failed to update settings',
        });
        throw error;
      }
    },
  };
};

// Utility functions for common operations
export const useStoreUtils = () => {
  const store = useAppStore();

  return {
    // Clear old data to prevent storage bloat
    clearOldData: () => {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Clear old food logs
      const recentFoodLogs = store.foodLogs.filter(
        (log) => log.timestamp >= thirtyDaysAgo,
      );

      // Clear old daily metrics
      const recentMetrics = store.dailyMetrics.filter(
        (metric) => metric.date >= thirtyDaysAgo,
      );

      // Update store with filtered data
      useAppStore.setState({
        foodLogs: recentFoodLogs,
        dailyMetrics: recentMetrics,
      });
    },

    // Get store size for monitoring
    getStoreSize: () => {
      const state = store;
      return {
        foodLogs: state.foodLogs.length,
        dailyMetrics: state.dailyMetrics.length,
        medications: state.medications.length,
        notifications: state.notifications.length,
      };
    },
  };
};

// Export the store for direct access if needed (use sparingly)
export default useAppStore;
