import { useCallback, useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useAppStore } from '../index';

// Performance-optimized hooks for specific data
export const useAuthUser = () => useAppStore((state) => state.user);
export const useThemeColors = () => useAppStore((state) => state.theme.colors);
export const useIsDarkTheme = () => useAppStore((state) => state.isDark);
export const useIsAuthenticated = () =>
  useAppStore((state) => state.isAuthenticated);

// Memoized computed hooks
export const useDailyNutritionMemo = (date?: Date) => {
  return useAppStore(
    useCallback((state) => state.getDailyNutrition(date), [date]),
  );
};

export const MealType = {
  breakfast: 'breakfast',
  lunch: 'lunch',
  dinner: 'dinner',
  snack: 'snack',
} as const;

export type MealType = (typeof MealType)[keyof typeof MealType];

export const useMealLogsMemo = (mealType: MealType, date?: Date) => {
  return useAppStore(
    useCallback((state) => state.getMealLogs(mealType, date), [mealType, date]),
  );
};

// Optimized notification hooks
export const useNotifications = () =>
  useAppStore(useShallow((state) => state.notifications));

export const useLatestNotification = () =>
  useAppStore((state) =>
    state.notifications.length > 0
      ? state.notifications[state.notifications.length - 1]
      : null,
  );

// Theme integration hooks
export const useThemeSync = () => {
  const { settings, setThemeMode } = useAppStore(
    useShallow((state) => ({
      settings: state.settings,
      setThemeMode: state.setThemeMode,
    })),
  );

  useEffect(() => {
    // Sync theme mode with settings
    setThemeMode(settings.themePreference);
  }, [settings.themePreference, setThemeMode]);
};

// Performance monitoring hooks
export const useStorePerformance = () => {
  const storeSize = useAppStore((state) => {
    const stateString = JSON.stringify(state);
    return {
      bytes: stateString.length,
      kb: Math.round(stateString.length / 1024),
    };
  });

  return storeSize;
};

// Data cleanup hooks
export const useDataCleanup = () => {
  const { foodLogs, dailyMetrics, notifications } = useAppStore(
    useShallow((state) => ({
      foodLogs: state.foodLogs,
      dailyMetrics: state.dailyMetrics,
      notifications: state.notifications,
    })),
  );

  const cleanupOldData = useCallback(() => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    const recentFoodLogs = foodLogs.filter(
      (log) => log.timestamp >= thirtyDaysAgo,
    );
    const recentMetrics = dailyMetrics.filter(
      (metric) => metric.date >= thirtyDaysAgo,
    );
    const recentNotifications = notifications.filter(
      (notification) => notification.timestamp >= oneHourAgo,
    );

    useAppStore.setState({
      foodLogs: recentFoodLogs,
      dailyMetrics: recentMetrics,
      notifications: recentNotifications,
    });
  }, [foodLogs, dailyMetrics, notifications]);

  return { cleanupOldData };
};

// Batch operations hooks
export const useBatchOperations = () => {
  const store = useAppStore();

  const batchAddFoodLogs = useCallback(
    (
      foodItems: {
        foodItem: any;
        mealType: MealType;
      }[],
    ) => {
      foodItems.forEach(({ foodItem, mealType }) => {
        store.addFoodLog(foodItem, mealType);
      });
    },
    [store],
  );

  const batchUpdateSettings = useCallback(
    (updates: Record<string, any>) => {
      Object.entries(updates).forEach(([key, value]) => {
        switch (key) {
          case 'theme':
            store.updateThemePreference(value);
            store.setThemeMode(value);
            break;
          case 'notifications':
            store.updateNotificationSettings(value);
            break;
          case 'language':
            store.updateLanguage(value);
            break;
          case 'units':
            store.updateUnits(value);
            break;
        }
      });
    },
    [store],
  );

  return {
    batchAddFoodLogs,
    batchUpdateSettings,
  };
};

// Validation hooks
export const useValidation = () => {
  const addFoodWithValidation = useCallback(
    (foodItem: any, mealType: MealType) => {
      if (!foodItem || !mealType) {
        throw new Error('Food item and meal type are required');
      }

      const store = useAppStore.getState();

      // Check liver-friendliness
      if (!foodItem.isLiverFriendly && foodItem.warnings?.length > 0) {
        store.addNotification({
          type: 'warning',
          message: `${foodItem.name} may not be suitable for liver health. ${foodItem.warnings.join(
            '. ',
          )}`,
        });
      }

      store.addFoodLog(foodItem, mealType);

      // Check daily limits
      const dailyNutrition = store.getDailyNutrition();
      if (dailyNutrition.sodium > 2300) {
        store.addNotification({
          type: 'warning',
          message:
            'Daily sodium limit exceeded. Consider reducing sodium intake.',
        });
      }

      store.addNotification({
        type: 'success',
        message: `${foodItem.name} added to ${mealType}`,
      });
    },
    [],
  );

  return { addFoodWithValidation };
};

// Development hooks
export const useDevTools = () => {
  const logState = useCallback(() => {
    console.log('Current store state:', useAppStore.getState());
  }, []);

  const logPerformance = useCallback(() => {
    const state = useAppStore.getState();
    const stateSize = JSON.stringify(state).length;
    console.log('Store performance:', {
      sizeInBytes: stateSize,
      sizeInKB: Math.round(stateSize / 1024),
      foodLogs: state.foodLogs.length,
      dailyMetrics: state.dailyMetrics.length,
      medications: state.medications.length,
      notifications: state.notifications.length,
    });
  }, []);

  return {
    logState,
    logPerformance,
  };
};
