import { FoodItem } from '../store/types';

// Static food database - moved out of store for better performance
export const FOOD_DATABASE: FoodItem[] = [
  {
    id: '1',
    name: 'Grilled Chicken Breast',
    sodium: 74,
    protein: 31,
    fat: 3,
    calories: 165,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '2',
    name: 'Canned Soup',
    sodium: 890,
    protein: 5,
    fat: 2,
    calories: 80,
    quantity: '1 cup',
    isLiverFriendly: false,
    warnings: ['High sodium content - may cause fluid retention'],
  },
  {
    id: '3',
    name: 'Fresh Salmon',
    sodium: 52,
    protein: 22,
    fat: 12,
    calories: 208,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '4',
    name: 'Processed Cheese',
    sodium: 1671,
    protein: 25,
    fat: 33,
    calories: 375,
    quantity: '100g',
    isLiverFriendly: false,
    warnings: [
      'Very high sodium',
      'High fat content - may worsen liver condition',
    ],
  },
  {
    id: '5',
    name: '<PERSON>',
    sodium: 5,
    protein: 2.6,
    fat: 0.9,
    calories: 111,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '6',
    name: 'Spinach',
    sodium: 79,
    protein: 2.9,
    fat: 0.4,
    calories: 23,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '7',
    name: 'Avocado',
    sodium: 7,
    protein: 2,
    fat: 15,
    calories: 160,
    quantity: '100g',
    isLiverFriendly: true,
    warnings: [],
  },
  {
    id: '8',
    name: 'Bacon',
    sodium: 1717,
    protein: 37,
    fat: 42,
    calories: 541,
    quantity: '100g',
    isLiverFriendly: false,
    warnings: [
      'Very high sodium content',
      'High saturated fat - harmful for liver health',
      'Processed meat - contains preservatives',
    ],
  },
];

// Optimized search function with memoization
let searchCache = new Map<string, FoodItem[]>();

export const searchFoodDatabase = (query: string): FoodItem[] => {
  const normalizedQuery = query.toLowerCase().trim();
  
  if (!normalizedQuery) {
    return FOOD_DATABASE;
  }

  // Check cache first
  if (searchCache.has(normalizedQuery)) {
    return searchCache.get(normalizedQuery)!;
  }

  const results = FOOD_DATABASE.filter((food) =>
    food.name.toLowerCase().includes(normalizedQuery)
  );

  // Cache results (limit cache size to prevent memory issues)
  if (searchCache.size > 100) {
    searchCache.clear();
  }
  searchCache.set(normalizedQuery, results);

  return results;
};

// Get food item by ID (optimized with Map for O(1) lookup)
const foodMap = new Map(FOOD_DATABASE.map(food => [food.id, food]));

export const getFoodById = (id: string): FoodItem | undefined => {
  return foodMap.get(id);
};

// Get liver-friendly foods
export const getLiverFriendlyFoods = (): FoodItem[] => {
  return FOOD_DATABASE.filter(food => food.isLiverFriendly);
};

// Get foods by category (could be extended with more categories)
export const getFoodsByCategory = (category: 'low-sodium' | 'high-protein' | 'low-fat'): FoodItem[] => {
  switch (category) {
    case 'low-sodium':
      return FOOD_DATABASE.filter(food => food.sodium < 100);
    case 'high-protein':
      return FOOD_DATABASE.filter(food => food.protein > 20);
    case 'low-fat':
      return FOOD_DATABASE.filter(food => food.fat < 5);
    default:
      return [];
  }
};

// Clear search cache (useful for memory management)
export const clearFoodSearchCache = (): void => {
  searchCache.clear();
};
