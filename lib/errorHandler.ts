import { AuthError, PostgrestError } from "@supabase/supabase-js";

export interface AppError {
  type: "auth" | "database" | "network" | "validation" | "unknown";
  message: string;
  originalError?: any;
  code?: string;
}

export function handleSupabaseError(
  error: PostgrestError | AuthError | Error,
): AppError {
  console.error("Supabase error:", error);

  // Auth errors
  if ("status" in error && error.message) {
    switch (error.message) {
      case "Invalid login credentials":
        return {
          type: "auth",
          message:
            "Invalid email or password. Please check your credentials and try again.",
          originalError: error,
        };
      case "Email not confirmed":
        return {
          type: "auth",
          message:
            "Please check your email and click the verification link before signing in.",
          originalError: error,
        };
      case "Email rate limit exceeded":
        return {
          type: "auth",
          message:
            "Too many requests. Please wait a moment before trying again.",
          originalError: error,
        };
      default:
        return {
          type: "auth",
          message: error.message || "Authentication failed. Please try again.",
          originalError: error,
        };
    }
  }

  // Database errors
  if ("code" in error && error.code) {
    switch (error.code) {
      case "23505": // Unique violation
        return {
          type: "database",
          message:
            "This record already exists. Please use different information.",
          originalError: error,
          code: error.code,
        };
      case "23503": // Foreign key violation
        return {
          type: "database",
          message: "Unable to save data due to missing related information.",
          originalError: error,
          code: error.code,
        };
      case "23514": // Check violation
        return {
          type: "database",
          message:
            "Invalid data provided. Please check your input and try again.",
          originalError: error,
          code: error.code,
        };
      case "PGRST116": // Row level security
        return {
          type: "auth",
          message: "You do not have permission to access this data.",
          originalError: error,
          code: error.code,
        };
      default:
        return {
          type: "database",
          message: "Database error occurred. Please try again later.",
          originalError: error,
          code: error.code,
        };
    }
  }

  // Network errors
  if (error.message?.includes("fetch")) {
    return {
      type: "network",
      message:
        "Network connection error. Please check your internet connection and try again.",
      originalError: error,
    };
  }

  // Default error
  return {
    type: "unknown",
    message: error.message || "An unexpected error occurred. Please try again.",
    originalError: error,
  };
}

export function getErrorMessage(error: any): string {
  const appError = handleSupabaseError(error);
  return appError.message;
}

export class AppErrorHandler {
  static logError(error: AppError, context?: string) {
    console.error(
      `[${error.type.toUpperCase()}]${context ? ` ${context}:` : ""}`,
      {
        message: error.message,
        code: error.code,
        originalError: error.originalError,
      },
    );
  }

  static async reportError(error: AppError, userId?: string, context?: string) {
    // In a production app, you might want to send errors to a service like Sentry
    this.logError(error, context);

    // Store critical errors in the database for monitoring
    if (error.type === "database" || error.type === "unknown") {
      try {
        // You could implement error logging to a dedicated table
        console.log("Would report error to monitoring service:", {
          userId,
          errorType: error.type,
          message: error.message,
          context,
          timestamp: new Date().toISOString(),
        });
      } catch (reportingError) {
        console.error("Failed to report error:", reportingError);
      }
    }
  }
}
