// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Disable Hermes for web platform to fix import.meta issues
config.transformer = {
  ...config.transformer,
  unstable_allowRequireContext: true,
  hermes: {
    ...config.transformer.hermes,
    // Disable Hermes transforms for web
    transform:
      process.env.EXPO_PLATFORM === 'web'
        ? false
        : config.transformer.hermes?.transform,
  },
};

module.exports = config;
