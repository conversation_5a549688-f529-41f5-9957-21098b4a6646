import type { Database } from './database';

export type UserRole = Database['public']['Enums']['user_role'];
export type OrganizationType = Database['public']['Enums']['organization_type'];
export type SeverityLevel = Database['public']['Enums']['severity_level'];

export type User = Database['public']['Tables']['users']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];

export type MedicalProfile =
  Database['public']['Tables']['medical_profiles']['Row'];
export type MedicalProfileInsert =
  Database['public']['Tables']['medical_profiles']['Insert'];
export type MedicalProfileUpdate =
  Database['public']['Tables']['medical_profiles']['Update'];

export type OnboardingData =
  Database['public']['Tables']['onboarding_data']['Row'];
export type OnboardingDataInsert =
  Database['public']['Tables']['onboarding_data']['Insert'];
export type OnboardingDataUpdate =
  Database['public']['Tables']['onboarding_data']['Update'];

export type Organization = Database['public']['Tables']['organizations']['Row'];
export type OrganizationInsert =
  Database['public']['Tables']['organizations']['Insert'];
export type OrganizationUpdate =
  Database['public']['Tables']['organizations']['Update'];

export interface AuthContextType {
  user: User | null;
  session: any;
  loading: boolean;
  signUp: (
    email: string,
    password: string,
    userData: Partial<UserInsert>,
  ) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<any>;
  updateProfile: (updates: UserUpdate) => Promise<any>;
}

export interface OnboardingContextType {
  currentStep: number;
  totalSteps: number;
  isComplete: boolean;
  stepData: Record<number, any>;
  nextStep: () => void;
  previousStep: () => void;
  updateStepData: (step: number, data: any) => void;
  completeOnboarding: () => Promise<void>;
  saveProgress: () => Promise<void>;
}
