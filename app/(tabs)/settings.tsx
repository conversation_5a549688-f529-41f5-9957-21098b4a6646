import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
} from 'react-native';
import {
  User,
  Bell,
  Shield,
  Heart,
  CircleHelp as HelpCircle,
  FileText,
  Smartphone,
  ChevronRight,
  CreditCard as Edit,
  Save,
  Moon,
  Sun,
  Monitor,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth, useSettings } from '@/lib/store';
import {
  useThemeColors,
  useBatchOperations,
  useIsDarkTheme,
} from '@/lib/store/hooks';

export default function SettingsScreen() {
  const colors = useThemeColors();
  const isDark = useIsDarkTheme();
  const { user, updateUser } = useAuth();

  const { batchUpdateSettings } = useBatchOperations();
  const {
    settings,
    updateThemePreference,
    updateNotificationSettings,
    saveSettings,
  } = useSettings();

  const [editingProfile, setEditingProfile] = useState(false);
  const [localProfile, setLocalProfile] = useState({
    name: user ? `${user.firstName} ${user.lastName}` : '',
    age: user?.profile.age.toString() || '',
    weight: user?.profile.weight.toString() || '',
    height: user?.profile.height.toString() || '',
    liverCondition: user?.profile.liverCondition || '',
    diagnosisDate:
      user?.profile.diagnosisDate.toISOString().split('T')[0] || '',
    healthcareProvider: user?.profile.healthcareProvider || '',
  });

  const saveProfile = () => {
    if (user) {
      const [firstName, ...lastNameParts] = localProfile.name.split(' ');
      const lastName = lastNameParts.join(' ');

      updateUser({
        firstName,
        lastName,
        profile: {
          ...user.profile,
          age: parseInt(localProfile.age),
          weight: parseFloat(localProfile.weight),
          height: parseFloat(localProfile.height),
          liverCondition: localProfile.liverCondition,
          diagnosisDate: new Date(localProfile.diagnosisDate),
          healthcareProvider: localProfile.healthcareProvider,
        },
      });
    }
    setEditingProfile(false);
    Alert.alert('Success', 'Profile updated successfully');
  };

  const exportData = () => {
    Alert.alert(
      'Export Data',
      'Your health data will be exported as a PDF report. This may take a few moments.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => console.log('Exporting data...') },
      ]
    );
  };

  const clearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your health data. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => console.log('Clearing data...'),
        },
      ]
    );
  };

  const showPrivacyPolicy = () => {
    Alert.alert(
      'Privacy Policy',
      'This app follows HIPAA compliance guidelines. Your health data is encrypted and stored securely on your device. We do not share your personal information with third parties without your explicit consent.',
      [{ text: 'OK' }]
    );
  };

  const showHelp = () => {
    Alert.alert(
      'Help & Support',
      'For technical support, contact: <EMAIL>\n\nFor medical questions, consult your healthcare provider.',
      [{ text: 'OK' }]
    );
  };

  const getThemeIcon = (mode: string) => {
    switch (mode) {
      case 'light':
        return <Sun size={20} color={colors.textSecondary} />;
      case 'dark':
        return <Moon size={20} color={colors.textSecondary} />;
      case 'system':
        return <Monitor size={20} color={colors.textSecondary} />;
    }
  };

  const getThemeLabel = (mode: string) => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
    }
  };

  const showThemeSelector = () => {
    Alert.alert('Choose Theme', 'Select your preferred theme mode', [
      { text: 'Light', onPress: () => updateThemePreference('light') },
      { text: 'Dark', onPress: () => updateThemePreference('dark') },
      { text: 'System', onPress: () => updateThemePreference('system') },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const SettingRow = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
    showChevron = true,
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
    showChevron?: boolean;
  }) => (
    <TouchableOpacity
      style={[styles.settingRow, { borderBottomColor: colors.borderLight }]}
      onPress={onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>{icon}</View>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: colors.text }]}>
            {title}
          </Text>
          {subtitle && (
            <Text
              style={[styles.settingSubtitle, { color: colors.textSecondary }]}
            >
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.settingRight}>
        {rightElement}
        {showChevron && <ChevronRight size={20} color={colors.textSecondary} />}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Settings
          </Text>
          <Text
            style={[styles.headerSubtitle, { color: colors.textSecondary }]}
          >
            Manage your health profile
          </Text>
        </View>

        {/* Profile Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <User size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Profile Information
            </Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setEditingProfile(!editingProfile)}
            >
              {editingProfile ? (
                <Save size={18} color={colors.primary} />
              ) : (
                <Edit size={18} color={colors.primary} />
              )}
            </TouchableOpacity>
          </View>

          <View
            style={[styles.profileCard, { backgroundColor: colors.surface }]}
          >
            {editingProfile ? (
              <View style={styles.profileEditForm}>
                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Full Name
                  </Text>
                  <TextInput
                    style={[
                      styles.formInput,
                      {
                        borderColor: colors.border,
                        backgroundColor: colors.surface,
                        color: colors.text,
                      },
                    ]}
                    value={localProfile.name}
                    onChangeText={(text) =>
                      setLocalProfile({ ...localProfile, name: text })
                    }
                    placeholder="Enter your full name"
                    placeholderTextColor={colors.textTertiary}
                  />
                </View>

                <View style={styles.formRow}>
                  <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                    <Text style={[styles.formLabel, { color: colors.text }]}>
                      Age
                    </Text>
                    <TextInput
                      style={[
                        styles.formInput,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                          color: colors.text,
                        },
                      ]}
                      value={localProfile.age}
                      onChangeText={(text) =>
                        setLocalProfile({ ...localProfile, age: text })
                      }
                      placeholder="Age"
                      keyboardType="numeric"
                      placeholderTextColor={colors.textTertiary}
                    />
                  </View>
                  <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                    <Text style={[styles.formLabel, { color: colors.text }]}>
                      Weight (kg)
                    </Text>
                    <TextInput
                      style={[
                        styles.formInput,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                          color: colors.text,
                        },
                      ]}
                      value={localProfile.weight}
                      onChangeText={(text) =>
                        setLocalProfile({ ...localProfile, weight: text })
                      }
                      placeholder="Weight"
                      keyboardType="numeric"
                      placeholderTextColor={colors.textTertiary}
                    />
                  </View>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Liver Condition
                  </Text>
                  <TextInput
                    style={[
                      styles.formInput,
                      {
                        borderColor: colors.border,
                        backgroundColor: colors.surface,
                        color: colors.text,
                      },
                    ]}
                    value={localProfile.liverCondition}
                    onChangeText={(text) =>
                      setLocalProfile({ ...localProfile, liverCondition: text })
                    }
                    placeholder="Enter your liver condition"
                    placeholderTextColor={colors.textTertiary}
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.formLabel, { color: colors.text }]}>
                    Healthcare Provider
                  </Text>
                  <TextInput
                    style={[
                      styles.formInput,
                      {
                        borderColor: colors.border,
                        backgroundColor: colors.surface,
                        color: colors.text,
                      },
                    ]}
                    value={localProfile.healthcareProvider}
                    onChangeText={(text) =>
                      setLocalProfile({
                        ...localProfile,
                        healthcareProvider: text,
                      })
                    }
                    placeholder="Enter your doctor's name"
                    placeholderTextColor={colors.textTertiary}
                  />
                </View>

                <TouchableOpacity
                  style={[
                    styles.saveButton,
                    { backgroundColor: colors.primary },
                  ]}
                  onPress={saveProfile}
                >
                  <Text
                    style={[
                      styles.saveButtonText,
                      { color: colors.textInverse },
                    ]}
                  >
                    Save Changes
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.profileDisplay}>
                <View style={styles.profileItem}>
                  <Text
                    style={[
                      styles.profileLabel,
                      { color: colors.textSecondary },
                    ]}
                  >
                    Name
                  </Text>
                  <Text style={[styles.profileValue, { color: colors.text }]}>
                    {localProfile.name}
                  </Text>
                </View>
                <View style={styles.profileItem}>
                  <Text
                    style={[
                      styles.profileLabel,
                      { color: colors.textSecondary },
                    ]}
                  >
                    Age • Weight
                  </Text>
                  <Text style={[styles.profileValue, { color: colors.text }]}>
                    {localProfile.age} years • {localProfile.weight} kg
                  </Text>
                </View>
                <View style={styles.profileItem}>
                  <Text
                    style={[
                      styles.profileLabel,
                      { color: colors.textSecondary },
                    ]}
                  >
                    Condition
                  </Text>
                  <Text style={[styles.profileValue, { color: colors.text }]}>
                    {localProfile.liverCondition}
                  </Text>
                </View>
                <View style={styles.profileItem}>
                  <Text
                    style={[
                      styles.profileLabel,
                      { color: colors.textSecondary },
                    ]}
                  >
                    Healthcare Provider
                  </Text>
                  <Text style={[styles.profileValue, { color: colors.text }]}>
                    {localProfile.healthcareProvider}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Appearance Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            {getThemeIcon(settings.themePreference)}
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Appearance
            </Text>
          </View>

          <View
            style={[styles.settingsCard, { backgroundColor: colors.surface }]}
          >
            <SettingRow
              icon={getThemeIcon(settings.themePreference)}
              title="Theme"
              subtitle={`Currently using ${getThemeLabel(
                settings.themePreference
              )?.toLowerCase()} mode`}
              onPress={showThemeSelector}
            />
          </View>
        </View>

        {/* Notifications Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Bell size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Notifications
            </Text>
          </View>

          <View
            style={[styles.settingsCard, { backgroundColor: colors.surface }]}
          >
            <SettingRow
              icon={<Bell size={20} color={colors.textSecondary} />}
              title="Medication Reminders"
              subtitle="Get notified when it's time for medications"
              rightElement={
                <Switch
                  value={settings.notifications.medicationReminders}
                  onValueChange={(value) =>
                    updateNotificationSettings({ medicationReminders: value })
                  }
                  trackColor={{
                    false: colors.border,
                    true: colors.primary,
                  }}
                  thumbColor={colors.surface}
                />
              }
              showChevron={false}
            />

            <SettingRow
              icon={<Heart size={20} color={colors.textSecondary} />}
              title="Meal Reminders"
              subtitle="Reminders for meal times and tracking"
              rightElement={
                <Switch
                  value={settings.notifications.mealReminders}
                  onValueChange={(value) =>
                    updateNotificationSettings({ mealReminders: value })
                  }
                  trackColor={{
                    false: colors.border,
                    true: colors.primary,
                  }}
                  thumbColor={colors.surface}
                />
              }
              showChevron={false}
            />

            <SettingRow
              icon={<FileText size={20} color={colors.textSecondary} />}
              title="Weekly Reports"
              subtitle="Summary of your weekly progress"
              rightElement={
                <Switch
                  value={settings.notifications.weeklyReports}
                  onValueChange={(value) =>
                    updateNotificationSettings({ weeklyReports: value })
                  }
                  trackColor={{
                    false: colors.border,
                    true: colors.primary,
                  }}
                  thumbColor={colors.surface}
                />
              }
              showChevron={false}
            />
          </View>
        </View>

        {/* Data & Privacy Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Shield size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Data & Privacy
            </Text>
          </View>

          <View
            style={[styles.settingsCard, { backgroundColor: colors.surface }]}
          >
            <SettingRow
              icon={<FileText size={20} color={colors.textSecondary} />}
              title="Export Health Data"
              subtitle="Generate a comprehensive health report"
              onPress={exportData}
            />

            <SettingRow
              icon={<Shield size={20} color={colors.textSecondary} />}
              title="Privacy Policy"
              subtitle="Learn how we protect your data"
              onPress={showPrivacyPolicy}
            />

            <SettingRow
              icon={<Smartphone size={20} color={colors.textSecondary} />}
              title="Data Storage"
              subtitle="Your data is stored securely on device"
            />
          </View>
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <HelpCircle size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Support
            </Text>
          </View>

          <View
            style={[styles.settingsCard, { backgroundColor: colors.surface }]}
          >
            <SettingRow
              icon={<HelpCircle size={20} color={colors.textSecondary} />}
              title="Help & Support"
              subtitle="Get help with using the app"
              onPress={showHelp}
            />

            <SettingRow
              icon={<FileText size={20} color={colors.textSecondary} />}
              title="About"
              subtitle="Version 1.0.0 • Liver Health Companion"
            />
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <View
            style={[
              styles.dangerCard,
              {
                backgroundColor: isDark ? 'rgba(239, 68, 68, 0.1)' : '#FEF2F2',
                borderColor: isDark ? 'rgba(239, 68, 68, 0.3)' : '#FED7D7',
              },
            ]}
          >
            <Text style={[styles.dangerTitle, { color: colors.error }]}>
              Danger Zone
            </Text>
            <TouchableOpacity
              style={[styles.dangerButton, { backgroundColor: colors.error }]}
              onPress={clearData}
            >
              <Text
                style={[styles.dangerButtonText, { color: colors.textInverse }]}
              >
                Clear All Data
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginLeft: 8,
    flex: 1,
  },
  editButton: {
    padding: 8,
  },
  profileCard: {
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  profileDisplay: {
    gap: 16,
  },
  profileItem: {
    borderBottomWidth: 1,
    paddingBottom: 12,
  },
  profileLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  profileValue: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  profileEditForm: {
    gap: 16,
  },
  formGroup: {
    gap: 8,
  },
  formRow: {
    flexDirection: 'row',
  },
  formLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginBottom: 4,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  saveButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  saveButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  settingsCard: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dangerCard: {
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
  },
  dangerTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginBottom: 12,
  },
  dangerButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  dangerButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
  },
});
