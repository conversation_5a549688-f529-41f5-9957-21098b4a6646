import React, { useEffect } from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  runOnJS,
  Easing,
} from 'react-native-reanimated';

interface PageTransitionProps {
  readonly children: React.ReactNode;
  readonly isVisible: boolean;
  readonly onTransitionComplete?: () => void;
  readonly direction?: 'left' | 'right' | 'up' | 'down';
  readonly staggerDelay?: number;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function PageTransition({
  children,
  isVisible,
  onTransitionComplete,
  direction = 'right',
  staggerDelay = 50,
}: PageTransitionProps) {
  const opacity = useSharedValue(0);
  const translateX = useSharedValue(
    direction === 'right'
      ? screenWidth
      : direction === 'left'
      ? -screenWidth
      : 0
  );
  const translateY = useSharedValue(
    direction === 'down' ? screenHeight : direction === 'up' ? -screenHeight : 0
  );
  const scale = useSharedValue(0.95);

  useEffect(() => {
    if (isVisible) {
      // Entrance animation
      opacity.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });

      translateX.value = withTiming(0, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });

      translateY.value = withTiming(0, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });

      scale.value = withSequence(
        withTiming(1.02, {
          duration: 200,
          easing: Easing.out(Easing.quad),
        }),
        withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad),
        })
      );

      // Call completion callback
      if (onTransitionComplete) {
        setTimeout(() => {
          runOnJS(onTransitionComplete)();
        }, 450);
      }
    } else {
      // Exit animation
      opacity.value = withTiming(0, {
        duration: 300,
        easing: Easing.in(Easing.cubic),
      });

      const exitTranslateX =
        direction === 'right'
          ? -screenWidth * 0.3
          : direction === 'left'
          ? screenWidth * 0.3
          : 0;
      const exitTranslateY =
        direction === 'down'
          ? -screenHeight * 0.3
          : direction === 'up'
          ? screenHeight * 0.3
          : 0;

      translateX.value = withTiming(exitTranslateX, {
        duration: 300,
        easing: Easing.in(Easing.cubic),
      });

      translateY.value = withTiming(exitTranslateY, {
        duration: 300,
        easing: Easing.in(Easing.cubic),
      });

      scale.value = withTiming(0.95, {
        duration: 300,
        easing: Easing.in(Easing.cubic),
      });
    }
  }, [
    isVisible,
    direction,
    opacity,
    translateX,
    translateY,
    scale,
    onTransitionComplete,
  ]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {children}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
