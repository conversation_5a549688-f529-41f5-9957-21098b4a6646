import React from 'react';
import {
  ScrollView,
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import Dropdown from '@/components/onboarding/Dropdown';
import FormField from '@/components/onboarding/FormField';
import DatePicker from '@/components/onboarding/DatePicker';
import MultiSelect from '@/components/onboarding/MultiSelect';
import {
  DIAGNOSIS_OPTIONS,
  DISEASE_STAGES,
  SECONDARY_CONDITIONS,
  TEST_TYPES,
} from '@/types/onboarding';
import { useThemeColors } from '@/lib/store/hooks';
import PageTransition from '@/components/ui/PageTransition';
import AnimatedHeader from '@/components/ui/AnimatedHeader';
import StaggeredEntrance from '@/components/ui/StaggeredEntrance';

export default function DiseaseHistoryStep() {
  const {
    diseaseHistory,
    setDiseaseHistory,
    errors,
    addTestResult,
    removeTestResult,
  } = useOnboarding();
  const colors = useThemeColors();

  return (
    <PageTransition isVisible={true} direction="right">
      <ScrollView
        style={styles.stepContent}
        showsVerticalScrollIndicator={false}
      >
        <AnimatedHeader
          title="Liver Disease History"
          subtitle="Please provide information about your liver condition and recent test results."
          isVisible={true}
          delay={0}
        />

        <StaggeredEntrance isVisible={true} delay={100} animationType="fadeUp">
          <Dropdown
            label="Primary Diagnosis"
            value={diseaseHistory.primaryDiagnosis ?? ''}
            options={DIAGNOSIS_OPTIONS.map((diagnosis) => ({
              value: diagnosis,
              label: diagnosis,
            }))}
            onSelect={(value) =>
              setDiseaseHistory({ ...diseaseHistory, primaryDiagnosis: value })
            }
            required
            error={errors['primaryDiagnosis']}
            accessibilityLabel="Primary diagnosis dropdown"
          />
        </StaggeredEntrance>

        {diseaseHistory.primaryDiagnosis === 'Other' && (
          <StaggeredEntrance
            isVisible={true}
            delay={150}
            animationType="fadeUp"
          >
            <FormField
              label="Other Diagnosis"
              value={diseaseHistory.otherDiagnosis ?? ''}
              onChangeText={(text) =>
                setDiseaseHistory({ ...diseaseHistory, otherDiagnosis: text })
              }
              placeholder="Please specify your diagnosis"
              required
              error={errors['otherDiagnosis']}
              accessibilityLabel="Other diagnosis input"
            />
          </StaggeredEntrance>
        )}

        <StaggeredEntrance isVisible={true} delay={200} animationType="fadeUp">
          <DatePicker
            label="Initial Diagnosis Date"
            value={
              diseaseHistory.diagnosisDate
                ? new Date(diseaseHistory.diagnosisDate)
                : null
            }
            onChange={(date) =>
              setDiseaseHistory({ ...diseaseHistory, diagnosisDate: date })
            }
            required
            error={errors['diagnosisDate']}
            maximumDate={new Date()}
            accessibilityLabel="Diagnosis date picker"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={250} animationType="fadeUp">
          <Dropdown
            label="Current Disease Stage"
            value={diseaseHistory.diseaseStage ?? ''}
            options={DISEASE_STAGES}
            onSelect={(value) =>
              setDiseaseHistory({
                ...diseaseHistory,
                diseaseStage: value as any,
              })
            }
            required
            error={errors['diseaseStage']}
            accessibilityLabel="Disease stage dropdown"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={300} animationType="fadeUp">
          <MultiSelect
            label="Secondary Conditions"
            values={diseaseHistory.secondaryConditions || []}
            options={SECONDARY_CONDITIONS.map((condition) => ({
              value: condition,
              label: condition,
            }))}
            onSelectionChange={(values) =>
              setDiseaseHistory({
                ...diseaseHistory,
                secondaryConditions: values,
              })
            }
            placeholder="Select any additional conditions"
            error={errors['secondaryConditions']}
            accessibilityLabel="Secondary conditions multi-select"
          />
        </StaggeredEntrance>

        <StaggeredEntrance isVisible={true} delay={350} animationType="fadeUp">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Latest Test Results
            </Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={addTestResult}
              accessibilityLabel="Add test result"
            >
              <Text
                style={[styles.addButtonText, { color: colors.textInverse }]}
              >
                Add Test
              </Text>
            </TouchableOpacity>
          </View>
        </StaggeredEntrance>

        {diseaseHistory.testResults?.map((test, index) => (
          <StaggeredEntrance
            key={`${test.testType}-${test.dateConducted}`}
            isVisible={true}
            delay={400 + index * 50}
            animationType="scale"
          >
            <View
              style={[
                styles.testResultCard,
                { backgroundColor: colors.surface },
              ]}
            >
              <View style={styles.cardHeader}>
                <Text style={[styles.cardTitle, { color: colors.text }]}>
                  Test Result #{index + 1}
                </Text>
                <TouchableOpacity
                  onPress={() => removeTestResult(index)}
                  style={styles.removeButton}
                  accessibilityLabel={`Remove test result ${index + 1}`}
                >
                  <Text
                    style={[styles.removeButtonText, { color: colors.error }]}
                  >
                    Remove
                  </Text>
                </TouchableOpacity>
              </View>

              <Dropdown
                label="Test Type"
                value={test.testType}
                options={TEST_TYPES.map((type) => ({
                  value: type,
                  label: type,
                }))}
                onSelect={(value) => {
                  const updatedResults = [
                    ...(diseaseHistory.testResults || []),
                  ];
                  updatedResults[index] = { ...test, testType: value };
                  setDiseaseHistory({
                    ...diseaseHistory,
                    testResults: updatedResults,
                  });
                }}
                required
                error={errors[`testResults.${index}.testType`]}
              />

              <DatePicker
                label="Date Conducted"
                value={test.dateConducted ? new Date(test.dateConducted) : null}
                onChange={(date) => {
                  const updatedResults = [
                    ...(diseaseHistory.testResults || []),
                  ];
                  updatedResults[index] = { ...test, dateConducted: date };
                  setDiseaseHistory({
                    ...diseaseHistory,
                    testResults: updatedResults,
                  });
                }}
                required
                error={errors[`testResults.${index}.dateConducted`]}
                maximumDate={new Date()}
              />

              <View style={styles.row}>
                <View style={styles.flex1}>
                  <FormField
                    label="Result"
                    value={test.result}
                    onChangeText={(text) => {
                      const updatedResults = [
                        ...(diseaseHistory.testResults || []),
                      ];
                      updatedResults[index] = { ...test, result: text };
                      setDiseaseHistory({
                        ...diseaseHistory,
                        testResults: updatedResults,
                      });
                    }}
                    placeholder="Enter result"
                    required
                    error={errors[`testResults.${index}.result`]}
                  />
                </View>
                <View style={styles.flex1}>
                  <FormField
                    label="Unit"
                    value={test.unit}
                    onChangeText={(text) => {
                      const updatedResults = [
                        ...(diseaseHistory.testResults || []),
                      ];
                      updatedResults[index] = { ...test, unit: text };
                      setDiseaseHistory({
                        ...diseaseHistory,
                        testResults: updatedResults,
                      });
                    }}
                    placeholder="mg/dL, U/L, etc."
                    required
                    error={errors[`testResults.${index}.unit`]}
                  />
                </View>
              </View>

              <FormField
                label="Lab/Facility Name"
                value={test.labName}
                onChangeText={(text) => {
                  const updatedResults = [
                    ...(diseaseHistory.testResults || []),
                  ];
                  updatedResults[index] = { ...test, labName: text };
                  setDiseaseHistory({
                    ...diseaseHistory,
                    testResults: updatedResults,
                  });
                }}
                placeholder="Enter lab or facility name"
                required
                error={errors[`testResults.${index}.labName`]}
              />
            </View>
          </StaggeredEntrance>
        ))}
      </ScrollView>
    </PageTransition>
  );
}

const styles = StyleSheet.create({
  stepContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 16,
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 24,
  },
  addButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
  },
  testResultCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  removeButton: {
    padding: 8,
  },
  removeButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  flex1: {
    flex: 1,
  },
});
