import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
} from 'react-native';
import {
  ChevronDown,
  Check,
  CircleAlert as AlertCircle,
} from 'lucide-react-native';
import { useThemeColors } from '@/lib/store/hooks';

interface MultiSelectOption {
  readonly value: string;
  readonly label: string;
}

interface MultiSelectProps {
  readonly label: string;
  readonly values: string[];
  readonly options: MultiSelectOption[];
  readonly onSelectionChange: (values: string[]) => void;
  readonly placeholder?: string;
  readonly error?: string;
  readonly required?: boolean;
  readonly accessibilityLabel?: string;
}

export default function MultiSelect({
  label,
  values,
  options,
  onSelectionChange,
  placeholder = 'Select options',
  error,
  required = false,
  accessibilityLabel,
}: MultiSelectProps) {
  const colors = useThemeColors();
  const [isOpen, setIsOpen] = useState(false);

  const selectedOptions = options.filter((option) =>
    values.includes(option.value),
  );

  const handleToggle = (optionValue: string) => {
    const newValues = values.includes(optionValue)
      ? values.filter((v) => v !== optionValue)
      : [...values, optionValue];
    onSelectionChange(newValues);
  };

  const getDisplayText = () => {
    if (selectedOptions.length === 0) return placeholder;
    if (selectedOptions.length === 1) return selectedOptions[0].label;
    return `${selectedOptions.length} selected`;
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: colors.text }]}>
        {label}
        {required && (
          <Text style={[styles.required, { color: colors.error }]}> *</Text>
        )}
      </Text>

      <TouchableOpacity
        style={[
          styles.multiSelect,
          {
            borderColor: error ? colors.error : colors.border,
            backgroundColor: colors.surface,
          },
        ]}
        onPress={() => setIsOpen(true)}
        accessibilityLabel={accessibilityLabel ?? `${label} multi-select`}
        accessibilityHint="Tap to select multiple options"
        accessibilityRole="button"
      >
        <Text
          style={[
            styles.multiSelectText,
            {
              color:
                selectedOptions.length > 0 ? colors.text : colors.textTertiary,
            },
          ]}
        >
          {getDisplayText()}
        </Text>
        <ChevronDown size={20} color={colors.textSecondary} />
      </TouchableOpacity>

      {selectedOptions.length > 0 && (
        <View style={styles.selectedContainer}>
          {selectedOptions.map((option) => (
            <View
              key={option.value}
              style={[
                styles.selectedTag,
                { backgroundColor: colors.primaryLight },
              ]}
            >
              <Text style={[styles.selectedTagText, { color: colors.primary }]}>
                {option.label}
              </Text>
              <TouchableOpacity
                onPress={() => handleToggle(option.value)}
                style={styles.removeTag}
                accessibilityLabel={`Remove ${option.label}`}
              >
                <Text style={[styles.removeTagText, { color: colors.primary }]}>
                  ×
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={16} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
        </View>
      )}

      <Modal
        visible={isOpen}
        transparent
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[styles.modalContent, { backgroundColor: colors.surface }]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select {label}
              </Text>
              <TouchableOpacity
                onPress={() => setIsOpen(false)}
                style={styles.closeButton}
              >
                <Text
                  style={[styles.closeButtonText, { color: colors.primary }]}
                >
                  Done
                </Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.optionsList}>
              {options.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.option,
                    { borderBottomColor: colors.borderLight },
                  ]}
                  onPress={() => handleToggle(option.value)}
                  accessibilityLabel={option.label}
                  accessibilityRole="checkbox"
                  accessibilityState={{
                    checked: values.includes(option.value),
                  }}
                >
                  <Text style={[styles.optionText, { color: colors.text }]}>
                    {option.label}
                  </Text>
                  {values.includes(option.value) && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  required: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  multiSelect: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 14,
    minHeight: 48,
  },
  multiSelectText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    flex: 1,
  },
  selectedContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  selectedTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  selectedTagText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  removeTag: {
    marginLeft: 6,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeTagText: {
    fontFamily: 'Inter-Bold',
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 6,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  optionText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    flex: 1,
  },
});
