# Zustand Store Migration Audit Report

## Executive Summary

This audit identifies all locations in the LiverHealthV2 application where state management is currently handled outside of the new Zustand store implementation. The analysis reveals several legacy patterns that need migration to achieve a fully centralized state management architecture.

## Current Zustand Store Implementation Status

✅ **Implemented**: The Zustand store is comprehensive and well-structured with:

- Multiple slices (auth, nutrition, medication, settings, UI, theme)
- Optimized selectors with shallow comparison
- Action creators for complex operations
- An `initializeApp` function ready for integration
- Utility functions for store management

## Critical Findings: Legacy State Management Patterns

### 1. Context-based State Management (High Priority)

#### OnboardingContext (`lib/contexts/OnboardingContext.tsx`)

**Impact**: High - Duplicate state management system
**Pattern**: React Context with multiple useState hooks
**State Variables**:

- `personalInfo` - Personal information form data
- `diseaseHistory` - Medical history and test results
- `medications` - Current medications list
- `finalConfirmation` - Submission confirmation
- `currentStep` - Onboarding flow step tracking
- `isSubmitting` - Form submission status
- `errors` - Form validation errors

**Issues**:

- AsyncStorage persistence logic duplicated
- Complex validation logic isolated from main store
- State management methods (addTestResult, removeMedication, etc.)

**Migration Required**: Full migration to Zustand store with onboarding slice

#### AuthContext (`lib/contexts/AuthContext.tsx`)

**Impact**: High - Core authentication state
**Pattern**: React Context with useState
**State Variables**:

- `user` - Current user profile data
- `session` - Supabase session object
- `loading` - Authentication loading state

**Methods**:

- `signUp`, `signIn`, `signOut`
- `resetPassword`, `updateProfile`

**Issues**:

- Authentication state separate from main store
- User profile management isolated
- Supabase integration needs coordination with store

**Migration Required**: Integrate with existing auth slice in Zustand store

### 2. Component-level Local State (Medium Priority)

#### SettingsScreen (`app/(tabs)/settings.tsx`)

**Pattern**: Mixed - Uses both Zustand hooks AND local useState
**Local State Variables**:

- `editingProfile` - Edit mode toggle
- `localProfile` - Temporary profile edit data

**Current Zustand Usage**: ✅ Already using:

- `useAuth`, `useSettings`, `useBatchOperations`

**Migration Required**: Move edit state to UI slice

#### OnboardingScreen (`app/(tabs)/onboarding.tsx`)

**Impact**: High - Complete duplication of OnboardingContext
**Pattern**: Local useState hooks duplicating context state
**Duplicated State**:

- All the same state variables as OnboardingContext
- Identical AsyncStorage persistence logic
- Same validation and submission logic

**Migration Required**: Remove duplication, use single source of truth

#### FoodTrackingScreen (`app/(tabs)/food-tracking.tsx`)

**Pattern**: Mixed - Uses Zustand hooks with some local state
**Local State Variables**:

- `searchQuery` - Food search input
- `selectedMealType` - Current meal type selection

**Current Zustand Usage**: ✅ Already using:

- `useNutrition`, `useUI`, `useValidation`

**Migration Required**: Consider moving search state to UI slice

### 3. Custom Hooks with Local State (Low Priority)

#### usePageTransition (`lib/hooks/usePageTransition.ts`)

**Pattern**: Custom hook with internal useState
**State Variables**:

- `isLoading` - Page transition loading state
- `progress` - Loading progress percentage
- `isPageVisible` - Page visibility for animations

**Migration Consideration**: Could be moved to UI slice for global page transition management

#### useFrameworkReady (`lib/hooks/useFrameworkReady.ts`)

**Pattern**: Simple hook without state management
**Status**: ✅ No migration needed

### 4. Store Integration Status

#### Already Integrated Components

✅ **HomeScreen** (`app/(tabs)/index.tsx`):

- Uses `useDailyProgress`, `useTodaysMedications`
- Proper Zustand integration

✅ **MealPlanningScreen** (`app/(tabs)/meal-planning.tsx`):

- Uses theme hooks from store
- No local state management issues

✅ **HealthMetricsScreen** (`app/(tabs)/health-metrics.tsx`):

- Uses theme hooks from store
- No local state management issues

## Missing Store Integration

### initializeApp Function Integration

**Location**: `lib/store/index.ts` (lines 220-246)
**Status**: ❌ Not integrated into app bootstrap
**Required Integration Point**: `app/_layout.tsx` - RootLayout component

**Current RootLayout**:

```typescript
export default function RootLayout() {
  useFrameworkReady();
  // Font loading logic...

  return (
    <AuthProvider>
      <Stack screenOptions={{ headerShown: false }}>
```

**Required Change**: Call `initializeApp()` during app startup

## Migration Priority Matrix

| Component/Pattern | Priority | Complexity | Impact |
|------------------|----------|------------|---------|
| OnboardingContext + OnboardingScreen | **HIGH** | High | Critical - Duplicate systems |
| AuthContext Integration | **HIGH** | Medium | Core authentication |
| initializeApp Integration | **HIGH** | Low | Store initialization |
| SettingsScreen edit state | **MEDIUM** | Low | UI consistency |
| FoodTrackingScreen search state | **LOW** | Low | Nice to have |
| usePageTransition | **LOW** | Medium | Global transitions |

## Recommended Migration Steps

### Phase 1: Critical Infrastructure (High Priority)

1. **Integrate initializeApp function** in `app/_layout.tsx`
2. **Migrate AuthContext** to use Zustand auth slice
3. **Create onboarding slice** in Zustand store
4. **Migrate OnboardingContext** to Zustand
5. **Remove OnboardingScreen duplication**

### Phase 2: Component Cleanup (Medium Priority)

1. **Migrate SettingsScreen edit state** to UI slice
2. **Consolidate form validation** in store
3. **Update component imports** to use Zustand hooks

### Phase 3: Enhancement (Low Priority)

1. **Move search state** to UI slice
2. **Integrate page transitions** globally
3. **Optimize selectors** and performance

## Specific Files Requiring Changes

### High Priority Files

- `app/_layout.tsx` - Add initializeApp call
- `lib/contexts/AuthContext.tsx` - Migrate to Zustand
- `lib/contexts/OnboardingContext.tsx` - Replace with Zustand slice
- `app/(tabs)/onboarding.tsx` - Remove duplicate state logic
- `app/(tabs)/settings.tsx` - Move edit state to store

### Medium Priority Files

- `app/(tabs)/food-tracking.tsx` - Consider search state migration
- `lib/hooks/usePageTransition.ts` - Evaluate global integration

### Store Files to Extend

- `lib/store/slices/` - Add onboarding slice
- `lib/store/slices/authSlice.ts` - Enhance with Context functionality
- `lib/store/slices/uiSlice.ts` - Add edit states and search state
- `lib/store/index.ts` - Export new hooks and actions

## Data Migration Considerations

### AsyncStorage Coordination

- OnboardingContext uses AsyncStorage for persistence
- Need to coordinate with Zustand persistence middleware
- Ensure data migration path for existing users

### Supabase Integration

- AuthContext has direct Supabase integration
- Need to maintain authentication flow during migration
- Consider authentication state synchronization

### Form Validation

- Complex validation logic in OnboardingContext
- Need to preserve validation rules and error handling
- Consider centralized validation utilities

## Performance Impact Assessment

### Positive Impacts

- Reduced component re-renders with optimized selectors
- Eliminated prop drilling from Context patterns
- Centralized state updates and batch operations

### Migration Risks

- Temporary state inconsistency during migration
- Potential authentication flow disruption
- Form data loss if not properly migrated

## Testing Strategy

### Pre-Migration Testing

- Document current authentication flows
- Capture onboarding form state transitions
- Test AsyncStorage persistence behavior

### Post-Migration Validation

- Verify authentication state consistency
- Test onboarding flow completion
- Validate data persistence across app restarts
- Performance benchmarking of state updates

## Conclusion

The audit reveals a mixed state management architecture with significant opportunities for consolidation. The highest priority is eliminating the duplicate onboarding state management systems and integrating the comprehensive Zustand store that's already implemented. The migration will result in a cleaner, more maintainable codebase with better performance characteristics.

**Estimated Migration Effort**: 2-3 development days for Phase 1 critical changes, 1-2 additional days for complete cleanup.
