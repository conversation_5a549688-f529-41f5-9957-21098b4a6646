# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Start development server**: `npm run dev` (uses <PERSON> with telemetry disabled)
- **Build for web**: `npm run build:web`
- **Lint code**: `npm run lint` (uses Expo's built-in linter)

## Project Architecture

This is a React Native mobile application built with Expo Router and TypeScript, designed for liver health nutrition tracking.

### Core Architecture

- **Framework**: Expo SDK 53 with React Native 0.79.1
- **Navigation**: Expo Router with file-based routing
- **Language**: TypeScript with strict mode enabled
- **State Management**: React Context API for theme management
- **Form Validation**: Zod schemas with React Hook Form
- **Animations**: React Native Reanimated 3 for smooth transitions
- **Storage**: AsyncStorage for local data persistence
- **Database**: Expo SQLite for local data storage

### File Structure

```
app/
├── _layout.tsx           # Root layout with ThemeProvider and font loading
├── (tabs)/              # Tab-based navigation group
│   ├── _layout.tsx      # Tab navigation configuration
│   ├── index.tsx        # Home/Dashboard tab
│   ├── food-tracking.tsx
│   ├── meal-planning.tsx
│   ├── health-metrics.tsx
│   ├── settings.tsx
│   └── onboarding.tsx   # Hidden from tab bar (href: null)
└── +not-found.tsx       # 404 page

components/
├── onboarding/          # Onboarding-specific form components
└── ui/                  # Reusable UI components with custom animations

contexts/
└── ThemeContext.tsx     # Theme management with light/dark/system modes

types/
├── onboarding.ts        # Zod validation schemas and TypeScript types
└── env.d.ts            # Environment type declarations

hooks/
├── useFrameworkReady.ts # Web framework integration hook
└── usePageTransition.ts # Custom page transition logic
```

### Theme System

The app uses a comprehensive theme system with:

- **Theme modes**: light, dark, system (follows device preference)
- **Color palette**: Teal primary (`#14B8A6`) with semantic color tokens
- **Persistence**: Theme preference saved to AsyncStorage
- **Context**: Accessible via `useTheme()` hook throughout the app

### Navigation Patterns

- **Expo Router**: File-based routing with TypeScript support
- **Tab Navigation**: 5 main tabs (Home, Food Tracking, Meal Planning, Health Metrics, Settings)
- **Hidden Screens**: Onboarding accessible programmatically but hidden from tab bar
- **Transitions**: Custom page transitions using React Native Reanimated

### Form Handling

- **Validation**: Zod schemas in `types/onboarding.ts` with comprehensive medical data validation
- **Forms**: React Hook Form integration with custom form components
- **Data Types**: Strongly typed with medical-specific constants (diagnoses, medications, test types)

### Animation System

- **Library**: React Native Reanimated 3 for 60fps animations
- **Components**:
  - `PageTransition`: Directional page transitions with stagger support
  - `StaggeredEntrance`: Sequential element animations
  - `AnimatedHeader`/`AnimatedImage`: Specialized animated components
- **Patterns**: Consistent easing curves and timing across components

### Key Dependencies

- **UI**: Lucide React Native for icons, React Native Paper for Material components
- **Data**: Expo SQLite, AsyncStorage, date-fns for date handling
- **Forms**: React Hook Form with Hookform Resolvers for Zod integration
- **Charts**: React Native Chart Kit for health metrics visualization
- **Modals**: React Native Modal for overlay interfaces

### Medical Domain Specifics

This app handles sensitive medical data with:

- **Liver diseases**: Comprehensive diagnosis options (Hepatitis B/C, NAFLD, Cirrhosis, etc.)
- **Medications**: Common liver medication database with dosage tracking
- **Lab results**: Liver function test types (ALT, AST, Bilirubin, etc.)
- **Disease staging**: Mild/Moderate/Severe/End-stage classifications

### Development Notes

- **Path aliases**: Use `@/` for imports (configured in tsconfig.json)
- **Font loading**: Inter font family loaded asynchronously with splash screen management
- **Platform support**: Configured for iOS, web, and Android
- **New Architecture**: Expo's new architecture enabled for better performance
