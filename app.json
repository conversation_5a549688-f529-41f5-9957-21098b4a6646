{"expo": {"name": "bolt-expo-nativewind", "slug": "bolt-expo-nativewind", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "ios": {"supportsTablet": true}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-notifications", "expo-sqlite"], "experiments": {"typedRoutes": true}}}